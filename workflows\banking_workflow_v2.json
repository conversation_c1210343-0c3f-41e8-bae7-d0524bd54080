{"workflow": {"id": "banking_customer_service", "name": "Banking Customer Service", "version": "1.0", "start": "Greeting", "allowed_actions": ["Check account balance", "Transfer funds", "Apply for loan", "Report lost card", "Update personal information"], "prohibited_actions": ["Do not share PINs or passwords", "Do not process transactions without verification", "Do not disclose sensitive account details"], "interrupt_config": {"global_settings": {"enabled": false, "vad_threshold": 0.05, "confirmation_window_seconds": 0.5, "min_interrupt_duration_seconds": 0.3, "tts_interrupt_cooldown_seconds": 0, "vad_method": "webrtcvad", "webrtc_aggressiveness": 3, "required_consecutive_frames": 5, "user_speech_end_silence_seconds": 1.5}}, "states": {"Greeting": {"id": "Greeting", "type": "input", "layer2_id": "l2_greeting_banking_system_v2", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "true", "target": "Inquiry"}], "allowed_tools": ["STT", "LLM", "TTS", "CACHE"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Please let me finish my greeting, then I'll help you."}}, "Inquiry": {"id": "Inquiry", "type": "inform", "layer2_id": "l2_inquiry_banking_system_v2", "expected_input": ["text"], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "intent == 'account_balance'", "target": "CheckBalance"}, {"condition": "intent == 'fund_transfer'", "target": "TransferFunds"}, {"condition": "intent == 'exchange_rate'", "target": "exchangeRate"}, {"condition": "intent == 'goodbye'", "target": "Goodbye"}], "allowed_tools": ["STT", "LLM", "TTS", "CACHE"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Please let me finish my response, then I'll help you."}}, "CheckBalance": {"id": "CheckBalance", "type": "inform", "layer2_id": "l2_check_balance_banking_system_v2", "expected_input": ["account_id"], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "intent == 'account_balance'", "target": "CheckBalance"}, {"condition": "intent == 'fund_transfer'", "target": "TransferFunds"}, {"condition": "intent == 'exchange_rate'", "target": "exchangeRate"}, {"condition": "intent == 'goodbye'", "target": "Goodbye"}, {"condition": "true", "target": "Inquiry"}], "allowed_tools": ["LLM", "TTS", "DB_QUERY"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Please let me finish telling you your balance, then I'll help you."}}, "TransferFunds": {"id": "TransferFunds", "type": "transaction", "layer2_id": "l2_transfer_funds_banking_system_v2", "expected_input": ["source_account", "target_account", "amount"], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "intent == 'account_balance'", "target": "CheckBalance"}, {"condition": "intent == 'fund_transfer'", "target": "TransferFunds"}, {"condition": "intent == 'exchange_rate'", "target": "exchangeRate"}, {"condition": "intent == 'goodbye'", "target": "Goodbye"}, {"condition": "true", "target": "Inquiry"}], "allowed_tools": ["LLM", "TTS", "TRANSACTION_API"], "interrupt_config": {"enabled": true, "reversible": false, "interrupt_message": "This transfer cannot be undone. Please let me finish processing your transfer before you speak."}}, "exchangeRate": {"id": "exchangeRate", "type": "transaction", "layer2_id": "l2_exchange_rate_banking_system_v2", "expected_input": ["source_account", "target_account", "amount"], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "intent == 'account_balance'", "target": "CheckBalance"}, {"condition": "intent == 'fund_transfer'", "target": "TransferFunds"}, {"condition": "intent == 'exchange_rate'", "target": "exchangeRate"}, {"condition": "intent == 'goodbye'", "target": "Goodbye"}, {"condition": "true", "target": "Inquiry"}], "allowed_tools": ["LLM", "TTS", "TRANSACTION_API"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Please let me finish telling you the exchange rate, then I'll help you."}}, "Goodbye": {"id": "Goodbye", "type": "end", "layer2_id": "l2_goodbye_banking_system_v2", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [], "allowed_tools": ["TTS"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Please let me finish saying goodbye, then I'll help you."}}}}}